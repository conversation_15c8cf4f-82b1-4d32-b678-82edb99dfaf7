/* Main Container */
.delete-dialog-container {
  width: 100%;
  max-width: 800px;
  background-color: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  color: var(--text-primary);
  font-family: inherit;
  animation: dialogFadeIn 0.3s ease-out;
}

/* Animation */
@keyframes dialogFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Header */
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, var(--danger), var(--danger-dark));
  color: white;
  border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.header-icon {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
}

.dialog-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.close-btn {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Content */
.dialog-content {
  padding: 1.5rem;
}

/* Member Section */
.member-section {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  border-left: 4px solid var(--danger);
}

.member-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 1.125rem;
  flex-shrink: 0;
}

.member-details {
  flex: 1;
}

.member-name {
  margin: 0 0 0.25rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

.member-subtitle {
  margin: 0;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* Warning Section */
.warning-section {
  margin-bottom: 1.5rem;
}

.warning-box {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background-color: var(--warning-light);
  border: 1px solid var(--warning);
  border-radius: var(--border-radius-md);
  color: var(--warning-dark);
  font-size: 0.9rem;
  font-weight: 500;
}

.warning-box i {
  font-size: 1.125rem;
  flex-shrink: 0;
}

/* Selection Section */
.selection-section {
  margin-bottom: 1rem;
}

.selection-section h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

/* Memberships Container */
.memberships-container {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

/* Membership Card */
.membership-card {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-md);
  background-color: var(--bg-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.membership-card:hover {
  border-color: var(--danger);
  box-shadow: 0 2px 8px rgba(var(--danger-rgb), 0.1);
}

.membership-card.selected {
  border-color: var(--danger);
  background-color: var(--danger-light);
  box-shadow: 0 2px 12px rgba(var(--danger-rgb), 0.15);
}

.membership-radio {
  margin-top: 0.125rem;
}

.membership-radio input[type="radio"] {
  width: 18px;
  height: 18px;
  accent-color: var(--danger);
}

.membership-content {
  flex: 1;
  min-width: 0;
}

/* Membership Header */
.membership-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
  gap: 1rem;
}

.package-name {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.2;
  flex: 1;
}

.branch-name {
  font-size: 0.875rem;
  color: var(--text-secondary);
  background-color: var(--bg-tertiary);
  padding: 0.375rem 0.75rem;
  border-radius: var(--border-radius-sm);
  white-space: nowrap;
  font-weight: 500;
}

/* Membership Meta */
.membership-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.remaining-time,
.date-range {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.remaining-time {
  font-weight: 600;
}

.remaining-time i,
.date-range i {
  width: 14px;
  text-align: center;
  opacity: 0.7;
}

.remaining-time.status-good {
  color: var(--success);
}

.remaining-time.status-warning {
  color: var(--warning);
}

.remaining-time.status-danger {
  color: var(--danger);
}

.date-range {
  color: var(--text-secondary);
}

/* Membership Status */
.membership-status {
  margin-top: 0.5rem;
}

.frozen-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  background: linear-gradient(135deg, #74b9ff, #0984e3);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
}

/* Footer */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
  border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--border-radius-md);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  min-width: 120px;
  justify-content: center;
}

.btn i {
  font-size: 0.875rem;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn-cancel {
  background-color: transparent;
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.btn-cancel:hover:not(:disabled) {
  background-color: var(--bg-tertiary);
  border-color: var(--text-secondary);
}

.btn-delete {
  background-color: var(--danger);
  color: white;
}

.btn-delete:hover:not(:disabled) {
  background-color: var(--danger-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(var(--danger-rgb), 0.3);
}

/* Dark Mode */
[data-theme="dark"] .delete-dialog-container {
  background-color: var(--bg-secondary);
}

[data-theme="dark"] .member-section {
  background-color: var(--bg-tertiary);
}

[data-theme="dark"] .warning-box {
  background-color: rgba(255, 193, 7, 0.1);
  border-color: var(--warning);
}

[data-theme="dark"] .membership-card {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

[data-theme="dark"] .membership-card.selected {
  background-color: rgba(244, 67, 54, 0.1);
}

[data-theme="dark"] .branch-name {
  background-color: var(--bg-primary);
}

[data-theme="dark"] .dialog-footer {
  background-color: var(--bg-tertiary);
}

/* Responsive Design */
@media screen and (max-width: 768px) {
  .delete-dialog-container {
    max-width: 95vw;
    margin: 0 auto;
  }

  .dialog-header {
    padding: 0.75rem 1rem;
  }

  .dialog-title {
    font-size: 1.125rem;
  }

  .dialog-content {
    padding: 1rem;
  }

  .member-section {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .membership-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .membership-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .dialog-footer {
    flex-direction: column;
    padding: 1rem;
  }

  .btn {
    width: 100%;
    min-width: auto;
  }
}

@media screen and (max-width: 480px) {
  .dialog-header {
    padding: 0.5rem 0.75rem;
  }

  .dialog-content {
    padding: 0.75rem;
  }

  .member-avatar {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .package-name {
    font-size: 1rem;
  }

  .membership-card {
    padding: 0.75rem;
  }
}
