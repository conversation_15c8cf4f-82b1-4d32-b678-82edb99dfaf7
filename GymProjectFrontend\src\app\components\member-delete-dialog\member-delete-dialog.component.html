<div class="dialog-container">
  <h2 mat-dialog-title class="dialog-title">
    <i class="fas fa-trash-alt me-2"></i>
    Üyelik Silme - {{ data.memberName }}
  </h2>
  
  <mat-dialog-content class="dialog-content">
    <div class="warning-message">
      <i class="fas fa-exclamation-triangle me-2"></i>
      Bu üyenin {{ data.activeMemberships.length }} aktif üyeliği bulunmaktadır.
    </div>
    
    <p class="selection-text">Hangi üyeliği silmek istiyorsunuz?</p>
    
    <div class="membership-list">
      <div 
        *ngFor="let membership of data.activeMemberships" 
        class="membership-item"
        [class.selected]="selectedMembershipId === membership.membershipID"
        (click)="selectedMembershipId = membership.membershipID">
        
        <div class="membership-radio">
          <input 
            type="radio" 
            [value]="membership.membershipID"
            [(ngModel)]="selectedMembershipId"
            [id]="'membership-' + membership.membershipID">
        </div>
        
        <div class="membership-info">
          <div class="membership-title">
            {{ membership.branch }} - {{ membership.packageName }}
          </div>
          <div class="membership-details">
            <span class="remaining-days" 
                  [ngClass]="{
                    'text-success': membership.remainingDays > 10, 
                    'text-warning': membership.remainingDays <= 10 && membership.remainingDays > 3, 
                    'text-danger': membership.remainingDays <= 3
                  }">
              {{ membership.remainingDays }} gün kaldı
            </span>
            <span class="membership-dates">
              {{ membership.startDate | date:'dd.MM.yyyy' }} - {{ membership.endDate | date:'dd.MM.yyyy' }}
            </span>
          </div>
        </div>
        
        <div class="membership-status" *ngIf="membership.isFrozen">
          <span class="frozen-badge">
            <i class="fas fa-snowflake me-1"></i>
            Dondurulmuş
          </span>
        </div>
      </div>
    </div>
  </mat-dialog-content>
  
  <mat-dialog-actions class="dialog-actions">
    <button 
      mat-button 
      (click)="onCancel()"
      class="cancel-btn">
      İptal
    </button>
    <button 
      mat-raised-button 
      color="warn" 
      [disabled]="!selectedMembershipId"
      (click)="onDelete()"
      class="delete-btn">
      <i class="fas fa-trash-alt me-2"></i>
      Seçileni Sil
    </button>
  </mat-dialog-actions>
</div>
