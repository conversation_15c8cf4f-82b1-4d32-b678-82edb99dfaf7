.dialog-container {
  min-width: 650px;
  max-width: 800px;
  min-height: 400px;
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

.dialog-title {
  color: var(--danger);
  font-weight: 600;
  margin-bottom: 0;
  border-bottom: 2px solid var(--danger);
  padding-bottom: 15px;
  font-size: 1.25rem;
}

.dialog-content {
  padding: 20px 0;
}

.warning-message {
  background: var(--warning-light);
  border: 1px solid var(--warning);
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 25px;
  color: var(--warning);
  font-weight: 500;
  font-size: 1rem;
}

.selection-text {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 15px;
  color: var(--text-primary);
}

.membership-list {
  max-height: 300px;
  overflow-y: auto;
}

.membership-item {
  display: flex;
  align-items: center;
  padding: 20px;
  border: 2px solid var(--border-color);
  border-radius: 12px;
  margin-bottom: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: var(--bg-secondary);
  min-height: 80px;
}

.membership-item:hover {
  border-color: var(--primary);
  box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.15);
  transform: translateY(-2px);
}

.membership-item.selected {
  border-color: var(--primary);
  background: var(--primary-light);
  box-shadow: 0 4px 15px rgba(var(--primary-rgb), 0.2);
}

.membership-radio {
  margin-right: 15px;
}

.membership-radio input[type="radio"] {
  width: 20px;
  height: 20px;
  accent-color: var(--primary);
}

.membership-info {
  flex: 1;
}

.membership-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.membership-details {
  display: flex;
  gap: 15px;
  align-items: center;
}

.remaining-days {
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 14px;
}

.text-success {
  background: var(--success-light);
  color: var(--success);
}

.text-warning {
  background: var(--warning-light);
  color: var(--warning);
}

.text-danger {
  background: var(--danger-light);
  color: var(--danger);
}

.membership-dates {
  font-size: 14px;
  color: var(--text-secondary);
}

.membership-status {
  margin-left: 10px;
}

.frozen-badge {
  background: linear-gradient(135deg, #74b9ff, #0984e3);
  color: white;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

.dialog-actions {
  padding: 25px 0 0 0;
  border-top: 1px solid var(--border-color);
  justify-content: flex-end;
  gap: 15px;
}

.cancel-btn {
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  padding: 10px 24px;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
  background: transparent;
}

.cancel-btn:hover {
  background: var(--bg-tertiary);
  border-color: var(--text-secondary);
}

.delete-btn {
  background: var(--danger);
  color: white;
  border: none;
  padding: 10px 24px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s ease;
}

.delete-btn:hover:not(:disabled) {
  background: #c82333;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(var(--danger-rgb), 0.3);
}

.delete-btn:disabled {
  background: var(--secondary);
  color: white;
  cursor: not-allowed;
}

/* Dark mode CSS artık gerekli değil - CSS değişkenleri otomatik olarak dark mode'u destekliyor */
