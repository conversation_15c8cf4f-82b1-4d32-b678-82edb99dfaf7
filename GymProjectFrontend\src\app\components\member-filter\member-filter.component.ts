import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>nit, On<PERSON><PERSON>roy, AfterViewInit, ElementRef, ViewChild } from '@angular/core';
import { MemberService } from '../../services/member.service';
import { MembershipType } from '../../models/membershipType';
import { MembershipTypeService } from '../../services/membership-type.service';
import { MatDialog } from '@angular/material/dialog';
import { MembershipUpdateComponent } from '../crud/membership-update/membership-update.component';
import { faEdit, faSnowflake, faTrashAlt } from '@fortawesome/free-solid-svg-icons';
import { MembershipService } from '../../services/membership.service';
import { ToastrService } from 'ngx-toastr';
import { MemberFilter } from '../../models/memberFilter';
import { PackageWithCount } from '../../models/packageWithCount';
import { MembershipDetail } from '../../models/membershipDetail';
import { MemberDeleteDialogComponent } from '../member-delete-dialog/member-delete-dialog.component';
import { Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { FreezeMembershipDialogComponent } from '../freeze-membership-dialog/freeze-membership-dialog.component';
import { DialogService } from '../../services/dialog.service';
import { Chart, ChartConfiguration, ChartType } from 'chart.js';

@Component({
    selector: 'app-member-filter',
    templateUrl: './member-filter.component.html',
    styleUrls: ['./member-filter.component.css'],
    standalone: false
})
export class MemberFilterComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('genderChart') genderChartRef: ElementRef;
  genderChart: Chart;
  members: MemberFilter[] = [];
  activeMembersCount: number = 0;
  memberFilterText: string = '';
  private searchTextSubject = new Subject<string>();
  genderFilter: string = '';
  branchFilter: string = '';
  membershipTypes: MembershipType[] = [];
  faEdit = faEdit;
  faTrashAlt = faTrashAlt;
  faSnowflake = faSnowflake;
  isLoading: boolean = false;
  currentPage = 1;
  totalPages = 0;
  totalItems = 0;
  totalActiveMembers: number = 0;

  genderCounts = {
    all: 0,
    male: 0,
    female: 0
  };
  branchCounts: { [key: string]: number } = {};

  // Yeni özellikler - Çoklu paket filtreleme
  expandedBranches: Set<string> = new Set();
  selectedPackages: Set<number> = new Set();
  packagesByBranch: { [branch: string]: PackageWithCount[] } = {};
  isProcessing: boolean = false;
  lastClickTime: number = 0;

  constructor(
    private memberService: MemberService,
    private membershipTypeService: MembershipTypeService,
    private membershipService: MembershipService,
    private dialog: MatDialog,
    private toastrService: ToastrService,
    private dialogService: DialogService 
  ) {
    this.searchTextSubject.pipe(
      debounceTime(750),
      distinctUntilChanged()
    ).subscribe(searchText => {
      this.memberFilterText = searchText;
      this.currentPage = 1;
      this.loadMembers();
    });
  }

  ngOnInit(): void {
    this.getBranches();
    this.loadMembers();
    this.getTotalActiveMembers();
  }

  ngAfterViewInit(): void {
    setTimeout(() => {
      this.initGenderChart();
    }, 500);
  }

  ngOnDestroy(): void {
    this.searchTextSubject.complete();
    if (this.genderChart) {
      this.genderChart.destroy();
    }
  }

  initGenderChart(): void {
    const canvas = document.getElementById('genderChart') as HTMLCanvasElement;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    if (this.genderChart) {
      this.genderChart.destroy();
    }

    const chartData = {
      labels: ['Erkek', 'Kadın'],
      datasets: [{
        data: [this.genderCounts.male, this.genderCounts.female],
      backgroundColor: [
        'rgba(67, 97, 238, 0.7)',  // Erkek - Mavi
        'rgba(255, 105, 180, 0.7)'   // Kadın - Pembe
      ],
      borderColor: [
        'rgb(67, 97, 238)',
        'rgb(255, 105, 180)'
        ],
        borderWidth: 1,
        hoverOffset: 4
      }]
    };

    const config: ChartConfiguration = {
      type: 'doughnut' as ChartType,
      data: chartData,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom',
            labels: {
              padding: 20,
              usePointStyle: true,
              pointStyle: 'circle',
              font: {
                size: 12
              }
            }
          },
          tooltip: {
            callbacks: {
              label: function(context) {
                const label = context.label || '';
                const value = context.raw as number;
                const total = (context.dataset.data as number[]).reduce((a, b) => a + b, 0);
                const percentage = Math.round((value / total) * 100);
                return `${label}: ${value} (${percentage}%)`;
              }
            }
          }
        },
        animation: {
          duration: 1000
        },
        layout: {
          padding: {
            top: 10,
            bottom: 20
          }
        }
      }
    };

    this.genderChart = new Chart(ctx, config);
  }

  searchTextChanged(text: string) {
    this.searchTextSubject.next(text);
  }

  onFilterChange(): void {
    // Spam koruması
    const now = Date.now();
    if (this.isProcessing || (now - this.lastClickTime) < 1000) {
      return;
    }

    this.isProcessing = true;
    this.lastClickTime = now;
    this.currentPage = 1;

    if (this.selectedPackages.size > 0) {
      this.loadMembersByPackages();
    } else {
      this.loadMembers();
    }

    this.getTotalActiveMembers();

    setTimeout(() => {
      this.isProcessing = false;
    }, 500);
  }

  onBranchToggle(branch: string): void {
    // Spam koruması
    const now = Date.now();
    if (this.isProcessing || (now - this.lastClickTime) < 500) {
      return;
    }

    this.isProcessing = true;
    this.lastClickTime = now;

    // Eğer branş seçiliyse ve henüz açık değilse aç
    if (this.branchFilter === branch && !this.expandedBranches.has(branch)) {
      // Diğer branşları kapat, bu branşı aç
      this.expandedBranches.clear();
      this.expandedBranches.add(branch);
      this.clearPackageSelection();
      this.loadPackagesForBranch(branch);
    } else if (this.branchFilter !== branch) {
      // Farklı branş seçilirse tüm accordion'ları kapat
      this.expandedBranches.clear();
      this.clearPackageSelection();
    }

    setTimeout(() => {
      this.isProcessing = false;
    }, 300);
  }

  onBranchLabelClick(branch: string, event: Event): void {
    // Label tıklandığında radio button'ı seç ve accordion'ı aç/kapat
    event.preventDefault();

    // Radio button'ı seç
    this.branchFilter = branch;
    this.onFilterChange();

    // Accordion'ı toggle et
    if (this.expandedBranches.has(branch)) {
      this.expandedBranches.delete(branch);
      this.clearPackageSelection();
    } else {
      this.expandedBranches.clear();
      this.expandedBranches.add(branch);
      this.clearPackageSelection();
      this.loadPackagesForBranch(branch);
    }
  }

  loadPackagesForBranch(branch: string): void {
    this.membershipTypeService.getPackagesByBranch(branch).subscribe({
      next: (response) => {
        if (response.success) {
          this.packagesByBranch[branch] = response.data;
        }
      },
      error: (error) => {
        console.error('Error loading packages:', error);
        this.toastrService.error('Paketler yüklenirken hata oluştu');
      }
    });
  }

  onPackageToggle(packageId: number): void {
    if (this.selectedPackages.has(packageId)) {
      this.selectedPackages.delete(packageId);
    } else {
      this.selectedPackages.add(packageId);
    }
    // Artık otomatik arama yapmıyoruz, sadece seçimi güncelliyoruz
  }

  searchBySelectedPackages(): void {
    // Spam koruması
    const now = Date.now();
    if (this.isProcessing || (now - this.lastClickTime) < 1000) {
      return;
    }

    this.isProcessing = true;
    this.lastClickTime = now;
    this.currentPage = 1;

    if (this.selectedPackages.size > 0) {
      this.loadMembersByPackages();
    } else {
      this.loadMembers();
    }

    setTimeout(() => {
      this.isProcessing = false;
    }, 500);
  }

  clearPackageSelectionAndSearch(): void {
    // Spam koruması
    const now = Date.now();
    if (this.isProcessing || (now - this.lastClickTime) < 1000) {
      return;
    }

    this.isProcessing = true;
    this.lastClickTime = now;

    this.selectedPackages.clear();
    this.currentPage = 1;
    this.loadMembers();

    setTimeout(() => {
      this.isProcessing = false;
    }, 500);
  }

  clearPackageSelection(): void {
    this.selectedPackages.clear();
  }

  loadMembersByPackages(): void {
    this.isLoading = true;
    const packageIds = Array.from(this.selectedPackages);

    this.memberService.getMembersByMultiplePackages(
      packageIds,
      this.currentPage,
      this.memberFilterText
    ).subscribe({
      next: (response) => {
        if (response.success) {
          this.members = response.data.data;
          this.totalPages = response.data.totalPages;
          this.totalItems = response.data.totalCount;
          this.calculateActiveMembersCount();
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error fetching members by packages:', error);
        this.toastrService.error('Üyeler yüklenirken hata oluştu');
        this.isLoading = false;
      }
    });
  }

  getPackagesForBranch(branch: string): PackageWithCount[] {
    return this.packagesByBranch[branch] || [];
  }
  openFreezeDialog(member: MemberFilter): void {
    const dialogRef = this.dialog.open(FreezeMembershipDialogComponent, {
      width: '400px',
      data: { 
        memberName: member.name,
        membershipID: member.membershipID
      }
    });

    dialogRef.afterClosed().subscribe(freezeDays => {
      if (freezeDays) {
        this.membershipService.freezeMembership(member.membershipID, freezeDays).subscribe({
          next: (response) => {
            if (response.success) {
              this.toastrService.success('Üyelik başarıyla donduruldu');
              this.loadMembers();
            } else {
              this.toastrService.error(response.message);
            }
          },
          error: (error) => {
            this.toastrService.error('Üyelik dondurulurken bir hata oluştu');
          }
        });
      }
    });
  }
  getTotalActiveMembers() {
    this.memberService.getTotalActiveMembers().subscribe({
      next: (response) => {
        if (response.success) {
          this.totalActiveMembers = response.data;
          this.activeMembersCount = response.data;
        }
      },
      error: (error) => {
        console.error('Error fetching total members:', error);
      },
    });
  }

  loadMembers() {
    this.isLoading = true;
    const gender = this.genderFilter ? parseInt(this.genderFilter) : undefined;

    this.memberService
      .getMemberDetailsPaginated(
        this.currentPage,
        this.memberFilterText,
        gender,
        this.branchFilter
      )
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.members = response.data.data;
            this.totalPages = response.data.totalPages;
            this.totalItems = response.data.totalCount;
            this.calculateActiveMembersCount();
            this.calculateFilterCounts();
          }
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error fetching members:', error);
          this.toastrService.error(
            'Üyeler yüklenirken bir hata oluştu.',
            'Hata'
          );
          this.isLoading = false;
        },
      });
  }

  calculateFilterCounts() {
    this.genderCounts.all = this.totalItems;
  
    this.memberService.getActiveMemberCounts().subscribe({
      next: (response) => {
        if (response.success) {
          this.genderCounts.male = response.data['male'];
          this.genderCounts.female = response.data['female'];
          
          // Cinsiyet grafiğini güncelle
          if (this.genderChart) {
            this.genderChart.data.datasets[0].data = [
              this.genderCounts.male, 
              this.genderCounts.female
            ];
            this.genderChart.update();
          } else {
            this.initGenderChart();
          }
        }
      },
      error: (error) => {
        console.error('Error fetching gender counts:', error);
      }
    });
  
    this.memberService.getBranchCounts().subscribe({
      next: (response) => {
        if (response.success) {
          this.branchCounts = response.data;
        }
      },
      error: (error) => {
        console.error('Error fetching branch counts:', error);
      }
    });
  }

  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      this.currentPage = page;
      this.loadMembers();
    }
  }

  getBranches() {
    this.membershipTypeService.getMembershipTypes().subscribe((response) => {
      this.membershipTypes = this.getUniqueBranches(response.data);
    });
  }

  getUniqueBranches(membershipTypes: MembershipType[]): MembershipType[] {
    const uniqueBranches: MembershipType[] = [];
    const branchMap = new Map<string, boolean>();

    membershipTypes.forEach((type) => {
      if (!branchMap.has(type.branch)) {
        branchMap.set(type.branch, true);
        uniqueBranches.push(type);
      }
    });

    return uniqueBranches;
  }

  calculateActiveMembersCount() {
    this.activeMembersCount = this.members.filter((member) => {
      return member.remainingDays >= 0;
    }).length;
  }

  openUpdateDialog(member: MemberFilter): void {
    const dialogRef = this.dialog.open(MembershipUpdateComponent, {
      width: '400px',
      data: {
        membershipID: member.membershipID,
        memberID: member.memberID,
        membershipTypeID: member.membershipTypeID,
        startDate: member.startDate,
        endDate: member.endDate,
        name: member.name,
        branch: member.branch,
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.loadMembers();
      }
    });
  }

  deleteMember(member: MemberFilter) {
    // Önce üyenin tüm aktif üyeliklerini getir
    this.memberService.getMemberActiveMemberships(member.memberID).subscribe({
      next: (response) => {
        if (response.success && response.data.length > 1) {
          // Çoklu üyelik - dialog aç
          this.openDeleteDialog(member, response.data);
        } else if (response.data.length === 1) {
          // Tek üyelik - direkt onay dialog'u
          this.confirmSingleMembershipDelete(member, response.data[0]);
        } else {
          this.toastrService.error('Aktif üyelik bulunamadı');
        }
      },
      error: (error) => {
        console.error('Error fetching memberships:', error);
        this.toastrService.error('Üyelikler yüklenirken hata oluştu');
      }
    });
  }

  openDeleteDialog(member: MemberFilter, memberships: MembershipDetail[]): void {
    const dialogRef = this.dialog.open(MemberDeleteDialogComponent, {
      width: '700px',
      maxWidth: '90vw',
      maxHeight: '80vh',
      height: 'auto',
      disableClose: false,
      data: {
        memberName: member.name,
        memberId: member.memberID,
        activeMemberships: memberships
      }
    });

    dialogRef.afterClosed().subscribe(selectedMembershipId => {
      if (selectedMembershipId) {
        this.deleteMembershipById(selectedMembershipId);
      }
    });
  }

  confirmSingleMembershipDelete(member: MemberFilter, membership: MembershipDetail): void {
    this.dialogService.confirmMembershipDelete(member.name, member).subscribe(result => {
      if (result) {
        this.deleteMembershipById(membership.membershipID);
      }
    });
  }

  deleteMembershipById(membershipId: number): void {
    this.isLoading = true;
    this.membershipService.deleteMembershipById(membershipId).subscribe({
      next: (response) => {
        this.isLoading = false;
        if (response.success) {
          this.toastrService.success('Üyelik başarıyla silindi', 'Başarılı');
          this.loadMembers();
          this.getTotalActiveMembers();
        } else {
          this.toastrService.error(response.message, 'Hata');
        }
      },
      error: (error) => {
        this.isLoading = false;
        this.toastrService.error('Üyelik silinirken bir hata oluştu.', 'Hata');
      }
    });
  }
}