// src/app/services/dialog.service.ts
import { Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Observable } from 'rxjs';
import { ConfirmationDialogComponent } from '../components/confirmation-dialog/confirmation-dialog.component';
import { BirthdayPanelComponent } from '../components/birthday-panel/birthday-panel.component';
import { DialogType } from '../models/dialog-type.enum';
import { DialogData } from '../models/dialog.model';
import { Member } from '../models/member';
import { MemberBirthday } from '../models/member-birthday.model';
import { ExpenseDto } from '../models/expenseDto.model'; // Import mevcut

@Injectable({
  providedIn: 'root',
})
export class DialogService {
  constructor(private dialog: MatDialog) {}

  openDialog(data: DialogData): Observable<any> {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      width: this.getDialogWidth(data.type),
      data,
      disableClose: false,
      position: { top: '150px' },
      panelClass: 'custom-dialog-container'
    });

    return dialogRef.afterClosed();
  }

  private getDialogWidth(type: DialogType): string {
    switch (type) {
      case DialogType.PAYMENT:
        return '500px';
      case DialogType.UPDATE:
        return '600px';
      case DialogType.DELETE:
        return '650px';
      default:
        return '400px';
    }
  }

  confirm(title: string, message: string, details?: string): Observable<boolean> {
    return this.openDialog({
      type: DialogType.DELETE,
      title: title,
      message: details ? `${message}\n\n${details}` : message,
      confirmText: 'Evet',
      cancelText: 'Hayır'
    });
  }

  confirmDelete(itemName: string, item?: any): Observable<boolean> {
    return this.openDialog({
      type: DialogType.DELETE,
      title: 'Silme İşlemi',
      message: `${itemName} silmek istediğinizden emin misiniz?`,
      confirmText: 'Sil',
      cancelText: 'İptal',
      item,
    });
  }
  confirmProductDelete(itemName: string, item?: any): Observable<boolean> {
    return this.openDialog({
      type: DialogType.DELETE,
      title: 'Silme İşlemi',
      message: `${itemName} adlı ürünü silmek istediğinizden emin misiniz?`,
      confirmText: 'Sil',
      cancelText: 'İptal',
      item,
    });
  }
  confirmMemberDelete(itemName: string, item?: any): Observable<boolean> {
    return this.openDialog({
      type: DialogType.DELETE,
      title: 'ÜYE SİLME İŞLEMİ - DİKKAT!',
      message: `${itemName} adlı üyeyi sistemden kalıcı olarak silmek istediğinizden emin misiniz?\n\n⚠️ UYARI: Bu işlem geri alınamaz!\n\n• Üye sistemden tamamen silinecektir\n• Tüm üyelik geçmişi kaybolacaktır\n• Ödeme kayıtları silinecektir\n• Giriş-çıkış geçmişi kaybolacaktır\n• Bu üyeye tekrar üyelik verilemeyecektir`,
      confirmText: 'Evet, Kalıcı Olarak Sil',
      cancelText: 'İptal Et',
      item,
    });
  }
  confirmMembershipDelete(itemName: string, item?: any): Observable<boolean> {
    return this.openDialog({
      type: DialogType.DELETE,
      title: 'Üyelik Silme İşlemi',
      message: `${itemName} adlı üyenin üyeliğini silmek istediğinizden emin misiniz?\n\n⚠️ Bu işlemin sonuçları:\n\n• Seçilen üyelik kalıcı olarak silinecektir\n• Üye bu üyelikle giriş yapamayacaktır\n• Bu üyelik için yapılan ödemeler Kasa Raporundan silinecektir\n• Bu işlem geri alınamaz!`,
      confirmText: 'Sil',
      cancelText: 'İptal',
      item,
    });
  }

  confirmExerciseDelete(exerciseName: string, item?: any): Observable<boolean> {
    return this.openDialog({
      type: DialogType.DELETE,
      title: 'Egzersiz Silme İşlemi',
      message: `"${exerciseName}" adlı egzersizi silmek istediğinizden emin misiniz?\n\nBu işlem geri alınamaz.`,
      confirmText: 'Sil',
      cancelText: 'İptal',
      item,
    });
  }
  confirmFreezeCancel(
    memberName: string,
    usedDays: number
  ): Observable<boolean> {
    return this.openDialog({
      type: DialogType.DELETE,
      title: 'Dondurma İptal İşlemi',
      message: `${memberName} isimli üyenin dondurma işlemini tamamen iptal etmek istediğinizden emin misiniz?\n\nBu işlem sonucunda üyelik hiç dondurulmamış gibi eski haline dönecektir.`,
      confirmText: 'İptal Et',
      cancelText: 'Vazgeç',
    });
  }

  confirmReactivateFromToday(
    memberName: string,
    usedDays: number
  ): Observable<boolean> {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      width: '350px',
      data: {
        type: DialogType.UPDATE,
        title: 'Üyelik Aktifleştirme',
        message: `${memberName} isimli üyenin üyeliğini bugünden itibaren aktif etmek istediğinizden emin misiniz?\n\nKullanılan dondurma süresi: ${usedDays} gün\nBu işlem sonucunda üyelik süresi buna göre ayarlanacaktır. Bu işlem geri alınamaz.`,
        confirmText: 'Aktifleştir',
        cancelText: 'Vazgeç',
      },
      disableClose: true,
      position: { top: '150px' },
      panelClass: 'activation-dialog-container'
    });

    return dialogRef.afterClosed();
  }
  confirmPaymentDelete(
    memberName: string,
    paymentAmount: number,
    isDebtPayment: boolean
  ): Observable<boolean> {
    const paymentType = isDebtPayment ? 'borç ödemesi' : 'ödeme';
    const amount = paymentAmount.toLocaleString('tr-TR', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });

    return this.openDialog({
      type: DialogType.DELETE,
      title: 'Ödeme Silme İşlemi',
      message: `${memberName} adlı üyenin ${amount}₺ tutarındaki ${paymentType} kaydını silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.`,
      confirmText: 'Sil',
      cancelText: 'İptal',
    });
  }
  confirmMembershipTypeDelete(membershipType: any): Observable<boolean> {
    return this.openDialog({
      type: DialogType.DELETE,
      title: 'Üyelik Türü Silme İşlemi',
      message: `${membershipType.branch} branşına ait ${membershipType.typeName} paketini silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.`,
      confirmText: 'Sil',
      cancelText: 'İptal',
      item: membershipType,
    });
  }
  confirmExpenseDelete(expense: ExpenseDto): Observable<boolean> { // İlk eklenen hali
    const amount = expense.amount.toLocaleString('tr-TR', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
    return this.openDialog({
      type: DialogType.DELETE,
      title: 'Gider Silme İşlemi',
      message: `"${expense.description}" açıklamasındaki ${amount}₺ tutarındaki gideri silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.`, // Mesaj formatı burada
      confirmText: 'Sil',
      cancelText: 'İptal',
      item: expense,
    });
  }
  confirmUpdate(itemName: string, item?: any): Observable<boolean> {
    return this.openDialog({
      type: DialogType.UPDATE,
      title: 'Güncelleme İşlemi',
      message: `${itemName} güncellemek istediğinizden emin misiniz?`,
      confirmText: 'Güncelle',
      cancelText: 'İptal',
      item,
    });
  }

  confirmFreeze(itemName: string, item?: any): Observable<boolean> {
    return this.openDialog({
      type: DialogType.FREEZE,
      title: 'Dondurma İşlemi',
      message: `${itemName} üyeliğini dondurmak istediğinizden emin misiniz?`,
      confirmText: 'Dondur',
      cancelText: 'İptal',
      item,
    });
  }

  showPaymentDialog(
    itemName: string,
    paymentMethods: string[]
  ): Observable<any> {
    return this.openDialog({
      type: DialogType.PAYMENT,
      title: 'Ödeme İşlemi',
      message: `${itemName} için ödeme yöntemi seçin`,
      confirmText: 'Onayla',
      cancelText: 'İptal',
      paymentMethods,
      showPaymentMethods: true,
    });
  }

  confirmRevoke(userName: string): Observable<boolean> {
    return this.openDialog({
      type: DialogType.DELETE,
      title: 'Lisans İptal İşlemi',
      message: `${userName} kullanıcısının lisansını iptal etmek istediğinizden emin misiniz?`,
      confirmText: 'İptal Et',
      cancelText: 'Vazgeç'
    });
  }

  openBirthdayPanel(members: MemberBirthday[]): Observable<any> {
    const dialogRef = this.dialog.open(BirthdayPanelComponent, {
      width: '600px',
      data: { members },
      disableClose: false,
      position: { top: '100px' },
    });

    return dialogRef.afterClosed();
  }
}
