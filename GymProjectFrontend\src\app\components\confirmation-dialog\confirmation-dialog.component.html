<div class="modern-dialog zoom-in confirmation-dialog" [ngClass]="getDialogTypeClass()">
  <div class="modern-card-header">
    <h2>{{ data.title }}</h2>
    <button class="modern-btn-icon" (click)="onNoClick()">
      <i class="fas fa-times"></i>
    </button>
  </div>

  <div class="modern-card-body">
    <div class="dialog-icon" [ngClass]="getIconClass()">
      <i class="fas" [ngClass]="getIconType()"></i>
    </div>

    <div class="dialog-message" [ngClass]="{'detailed-warning': data.type === DialogType.DELETE}">
      <pre *ngIf="data.type === DialogType.DELETE; else normalMessage">{{ data.message }}</pre>
      <ng-template #normalMessage>{{ data.message }}</ng-template>
    </div>
  </div>

  <div class="modern-card-footer">
    <button
      type="button"
      class="modern-btn modern-btn-outline-secondary"
      (click)="onNoClick()">
      <i class="fas fa-times modern-btn-icon"></i> {{ data.cancelText || 'İptal' }}
    </button>
    <button
      type="button"
      class="modern-btn"
      [ngClass]="getButtonClass()"
      (click)="onYesClick()">
      <i class="fas" [ngClass]="getButtonIcon()" class="modern-btn-icon"></i> {{ data.confirmText || 'Onayla' }}
    </button>
  </div>
</div>

<style>
  .modern-dialog {
    min-width: 320px;
    max-width: 100%;
    overflow: hidden;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    background-color: var(--bg-primary);
    color: var(--text-primary);
  }

  .modern-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background-color: var(--bg-secondary);
    color: var(--text-primary);
  }

  .confirmation-dialog.delete-type .modern-card-header {
    background-color: var(--danger-light);
    color: var(--text-primary);
  }

  .confirmation-dialog.info-type .modern-card-header {
    background-color: var(--info-light);
    color: var(--text-primary);
  }

  .confirmation-dialog.warning-type .modern-card-header {
    background-color: var(--warning-light);
    color: var(--text-primary);
  }

  .confirmation-dialog.success-type .modern-card-header {
    background-color: var(--success-light);
    color: var(--text-primary);
  }

  .modern-card-header h2 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
  }

  .modern-card-body {
    padding: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .dialog-icon {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-md);
    font-size: 1.75rem;
  }

  .dialog-icon i {
    color: white;
  }

  .dialog-icon.warning {
    background-color: var(--warning);
  }

  .dialog-icon.danger {
    background-color: var(--danger);
  }

  .dialog-icon.info {
    background-color: var(--info);
  }

  .dialog-icon.success {
    background-color: var(--success);
  }

  .dialog-message {
    font-size: 1rem;
    margin: 0 0 var(--spacing-md);
    line-height: 1.5;
    max-width: 400px;
    color: var(--text-primary);
    white-space: pre-line;
  }

  .detailed-warning {
    text-align: left !important;
    max-width: 100%;
  }

  .detailed-warning pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: inherit;
    font-size: 0.95rem;
    line-height: 1.6;
    margin: 0;
    color: var(--text-primary);
    background: none;
    border: none;
    padding: 0;
  }

  .modern-card-footer {
    padding: var(--spacing-md) var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    background-color: var(--bg-secondary);
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-sm);
  }

  .modern-btn-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: none;
    background: transparent;
    color: var(--text-primary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 1rem;
  }

  .modern-btn-icon:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
  }

  /* Dark mode specific styles */
  [data-theme="dark"] .confirmation-dialog {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
  }

  [data-theme="dark"] .modern-card-header {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
  }

  [data-theme="dark"] .confirmation-dialog.delete-type .modern-card-header {
    background-color: rgba(244, 67, 54, 0.2);
    color: var(--text-primary);
  }

  [data-theme="dark"] .confirmation-dialog.info-type .modern-card-header {
    background-color: rgba(100, 181, 246, 0.2);
    color: var(--text-primary);
  }

  [data-theme="dark"] .confirmation-dialog.warning-type .modern-card-header {
    background-color: rgba(255, 193, 7, 0.2);
    color: var(--text-primary);
  }

  [data-theme="dark"] .confirmation-dialog.success-type .modern-card-header {
    background-color: rgba(76, 175, 80, 0.2);
    color: var(--text-primary);
  }

  [data-theme="dark"] .dialog-message,
  [data-theme="dark"] .detailed-warning pre {
    color: var(--text-primary);
  }

  [data-theme="dark"] .modern-card-footer {
    background-color: var(--bg-tertiary);
  }

  [data-theme="dark"] .modern-btn-icon {
    color: var(--text-primary);
  }

  [data-theme="dark"] .modern-btn-icon:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
  }

  /* Responsive Design */
  @media screen and (max-width: 768px) {
    .modern-dialog {
      min-width: 280px;
      max-width: 95vw;
    }

    .modern-card-header {
      padding: var(--spacing-sm) var(--spacing-md);
    }

    .modern-card-body {
      padding: var(--spacing-md);
    }

    .modern-card-footer {
      padding: var(--spacing-sm) var(--spacing-md);
      flex-direction: column;
    }

    .modern-card-footer .modern-btn {
      width: 100%;
      margin-bottom: var(--spacing-xs);
    }

    .modern-card-footer .modern-btn:last-child {
      margin-bottom: 0;
    }

    .dialog-icon {
      width: 48px;
      height: 48px;
      font-size: 1.25rem;
    }
  }

  @media screen and (max-width: 480px) {
    .modern-dialog {
      min-width: 260px;
    }

    .dialog-message {
      font-size: 0.875rem;
    }
  }
</style>
