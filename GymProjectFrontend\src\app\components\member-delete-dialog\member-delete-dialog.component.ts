import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MembershipDetail } from '../../models/membershipDetail';

export interface MemberDeleteDialogData {
  memberName: string;
  memberId: number;
  activeMemberships: MembershipDetail[];
}

@Component({
  selector: 'app-member-delete-dialog',
  standalone: false,
  templateUrl: './member-delete-dialog.component.html',
  styleUrls: ['./member-delete-dialog.component.css']
})
export class MemberDeleteDialogComponent {
  selectedMembershipId: number | null = null;

  constructor(
    public dialogRef: MatDialogRef<MemberDeleteDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: MemberDeleteDialogData
  ) {}

  onCancel(): void {
    this.dialogRef.close();
  }

  onDelete(): void {
    if (this.selectedMembershipId) {
      this.dialogRef.close(this.selectedMembershipId);
    }
  }
}
