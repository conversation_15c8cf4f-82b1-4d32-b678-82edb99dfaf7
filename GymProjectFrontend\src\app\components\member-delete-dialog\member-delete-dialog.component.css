.modern-dialog {
  min-width: 320px;
  max-width: 100%;
  max-height: 85vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

.modern-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  background-color: var(--danger-light);
  color: var(--text-primary);
}

.modern-card-header h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.modern-card-body {
  padding: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.dialog-icon {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-md);
  font-size: 1.75rem;
  background-color: var(--danger-light);
  color: var(--danger);
}

.member-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.modern-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
}

.member-name {
  font-weight: 600;
  color: var(--text-primary);
}

.dialog-message {
  font-size: 1rem;
  margin: 0 0 var(--spacing-md);
  line-height: 1.5;
  max-width: 400px;
  color: var(--text-primary);
}

.warning-info {
  width: 100%;
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-sm);
  background-color: var(--warning-light);
  border-radius: var(--border-radius-md);
  text-align: left;
}

.warning-info-item {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-xs);
  font-size: 0.875rem;
  color: var(--warning);
}

.warning-info-item:last-child {
  margin-bottom: 0;
}

.warning-info-item i {
  margin-right: var(--spacing-xs);
  color: var(--warning);
}

.delete-consequences {
  width: 100%;
  margin-top: var(--spacing-md);
  padding: var(--spacing-sm);
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  text-align: left;
  border: 1px solid var(--border-color);
}

.consequence-item {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-xs);
  font-size: 0.875rem;
  color: var(--text-primary);
}

.consequence-item:last-child {
  margin-bottom: 0;
}

.consequence-item i {
  margin-right: var(--spacing-xs);
  color: var(--danger);
  width: 16px;
  text-align: center;
}

.selection-text {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
  text-align: left;
  width: 100%;
}

.membership-list {
  max-height: 40vh;
  overflow-y: auto;
  width: 100%;
  margin-bottom: var(--spacing-md);
  padding-right: var(--spacing-xs);
}

.membership-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-md);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-md);
  margin-bottom: var(--spacing-sm);
  cursor: pointer;
  transition: all 0.3s ease;
  background: var(--bg-secondary);
  min-height: 80px;
}

.membership-item:hover {
  border-color: var(--danger);
  box-shadow: 0 4px 12px rgba(var(--danger-rgb), 0.15);
  transform: translateY(-2px);
}

.membership-item.selected {
  border-color: var(--danger);
  background: var(--danger-light);
  box-shadow: 0 4px 15px rgba(var(--danger-rgb), 0.2);
}

.membership-radio {
  margin-right: var(--spacing-sm);
}

.membership-radio input[type="radio"] {
  width: 20px;
  height: 20px;
  accent-color: var(--danger);
}

.membership-info {
  flex: 1;
}

.membership-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.membership-details {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
  flex-wrap: wrap;
}

.remaining-days {
  font-weight: 600;
  padding: 4px 8px;
  border-radius: var(--border-radius-sm);
  font-size: 0.875rem;
}

.text-success {
  background: var(--success-light);
  color: var(--success);
}

.text-warning {
  background: var(--warning-light);
  color: var(--warning);
}

.text-danger {
  background: var(--danger-light);
  color: var(--danger);
}

.membership-dates {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.membership-status {
  margin-left: var(--spacing-xs);
}

.frozen-badge {
  background: linear-gradient(135deg, #74b9ff, #0984e3);
  color: white;
  padding: 4px 8px;
  border-radius: var(--border-radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
}

.modern-card-footer {
  padding: var(--spacing-md) var(--spacing-lg);
  border-top: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
  flex-shrink: 0;
}

.modern-btn-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: none;
  background: transparent;
  color: var(--text-primary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.modern-btn-icon:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
}

/* Dark mode specific styles */
[data-theme="dark"] .delete-dialog {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

[data-theme="dark"] .modern-card-header {
  background-color: rgba(244, 67, 54, 0.2);
  color: var(--text-primary);
}

[data-theme="dark"] .dialog-message,
[data-theme="dark"] .member-name,
[data-theme="dark"] .selection-text {
  color: var(--text-primary);
}

[data-theme="dark"] .warning-info {
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-color);
}

[data-theme="dark"] .warning-info-item {
  color: var(--warning);
}

[data-theme="dark"] .delete-consequences {
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-color);
}

[data-theme="dark"] .consequence-item {
  color: var(--text-primary);
}

[data-theme="dark"] .modern-card-footer {
  background-color: var(--bg-tertiary);
}

[data-theme="dark"] .modern-btn-icon {
  color: var(--text-primary);
}

[data-theme="dark"] .modern-btn-icon:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
}

[data-theme="dark"] .membership-item {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

[data-theme="dark"] .membership-item:hover {
  border-color: var(--danger);
}

[data-theme="dark"] .membership-item.selected {
  background-color: rgba(244, 67, 54, 0.1);
  border-color: var(--danger);
}

/* Responsive Design */
@media screen and (max-width: 768px) {
  .modern-dialog {
    min-width: 280px;
    max-width: 95vw;
    max-height: 90vh;
  }

  .modern-card-header {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .modern-card-body {
    padding: var(--spacing-md);
  }

  .modern-card-footer {
    padding: var(--spacing-sm) var(--spacing-md);
    flex-direction: column;
  }

  .modern-card-footer .modern-btn {
    width: 100%;
    margin-bottom: var(--spacing-xs);
  }

  .modern-card-footer .modern-btn:last-child {
    margin-bottom: 0;
  }

  .dialog-icon {
    width: 48px;
    height: 48px;
    font-size: 1.25rem;
  }

  .membership-item {
    padding: var(--spacing-sm);
    min-height: 60px;
  }

  .membership-title {
    font-size: 1rem;
  }

  .membership-details {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .membership-list {
    max-height: 50vh;
  }
}

@media screen and (max-width: 480px) {
  .modern-dialog {
    min-width: 260px;
  }

  .dialog-message {
    font-size: 0.875rem;
  }

  .selection-text {
    font-size: 0.875rem;
  }
}
